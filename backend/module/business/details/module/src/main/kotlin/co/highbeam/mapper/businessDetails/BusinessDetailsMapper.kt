package co.highbeam.mapper.businessDetails

import co.highbeam.model.businessDetails.BusinessDetailsModel
import co.highbeam.model.businessDetails.InternalBusinessDetailsModel
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessDetails.BusinessDetailsRep
import co.highbeam.rep.businessDetails.InternalBusinessDetailsRep
import co.unit.rep.ApplicationRep
import co.unit.rep.CustomerRep
import com.google.inject.Inject
import mu.KotlinLogging

internal class BusinessDetailsMapper @Inject constructor() {
  fun map(details: BusinessDetailsModel): BusinessDetailsRep =
    BusinessDetailsRep(
      name = details.name,
      dba = details.dba,
      website = details.website,
      phoneNumber = details.phoneNumber,
    )

  fun map(updater: BusinessDetailsRep.Updater): BusinessDetailsModel.Updater =
    BusinessDetailsModel.Updater(
      dba = updater.dba,
      phoneNumber = updater.phoneNumber,
    )

  fun mapInternal(details: InternalBusinessDetailsModel): InternalBusinessDetailsRep =
    InternalBusinessDetailsRep(
      name = details.name,
      dba = details.dba,
      website = details.website,
      phoneNumber = details.phoneNumber,
      ein = details.ein,
      incorporationState = details.incorporationState,
      associatedPerson = details.associatedPerson,
    )
}
